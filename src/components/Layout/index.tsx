import { Link, Outlet, useLocation, useParams } from 'react-router-dom';
import { Layout, Menu } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import AppRoutes from '@/pages/routes';
import UserDropdown from "@/components/Layout/userManager";

const { Header, Sider, Content } = Layout;

export default () => {

  const location = useLocation(); // 获取当前路径和查询参数

  // 根据路径映射菜单选中状态
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path.startsWith('/overviewSummary')) return ['1'];
    if (path.startsWith('/analyze/comprehensive')) return ['2'];
    if (path.startsWith('/analyze/hourly')) return ['3'];
    if (path.startsWith('/budgetManager/management')) return ['4'];
    if (path.startsWith('/budgetManager/adSource')) return ['5'];
    if (path.startsWith('/flowManager/management')) return ['6'];
    if (path.startsWith('/flowManager/media')) return ['7'];
    if (path.startsWith('/flowManager/adSlot')) return ['8'];
    if (path.startsWith('/flowManager/adCode')) return ['9'];
    if (path.startsWith('/operationLog')) return ['10'];
    return [];
  };

  console.log('当前路径:', location.pathname); // 输出当前路径
  console.log('查询参数:', location.search); // 输出查询参数

  return (
    <Layout style={{
      width: '100%',
      height: '100vh',
      overflow: 'hidden'
    }}>
      <Header style={{
        background: '#fff',
        padding: '0 24px',
        position: 'fixed',
        zIndex: 1,
        width: '100%',
        display: 'flex',
        height: '56px',
        lineHeight: '56px', // 与 Sider 的高度保持一致，防止内容溢出
        alignItems: 'center',
        justifyContent: 'flex-end' // 将用户下拉组件对齐到右侧
      }}>
        <UserDropdown />
      </Header>
      <Layout style={{
        width: '100%',
        height: '100vh',
        overflow: 'hidden'
      }}>
        <Sider collapsible width={216}>
          <div className="logo" />
          <div style={{
            height: 'calc(100vh - 64px)',
            overflowY: 'auto',
            padding: '8px 0'
          }}>
            <Menu
              theme="dark"
              mode="inline"
              selectedKeys={getSelectedKeys()}
              defaultSelectedKeys={['1']}
            >
              {/*<Menu.Item key="1" icon={<DashboardOutlined />}>*/}
              {/*  <Link to="/overviewSummary">*/}
              {/*    概览*/}
              {/*  </Link>*/}
              {/*</Menu.Item>*/}
              {/*<Menu.SubMenu key="analyze" icon={<UserOutlined />} title="数据报表">*/}
              {/*  <Menu.Item key="2">*/}
              {/*    <Link to="/analyze/comprehensive">*/}
              {/*      综合报表*/}
              {/*    </Link>*/}
              {/*  </Menu.Item>*/}
              {/*  <Menu.Item key="3">*/}
              {/*    <Link to="/analyze/hourly">*/}
              {/*      小时数据*/}
              {/*    </Link>*/}
              {/*  </Menu.Item>*/}
              {/*</Menu.SubMenu>*/}
              <Menu.SubMenu key="budget" icon={<UserOutlined />} title="预算管理">
                <Menu.Item key="4">
                  <Link to="/budgetManager/management">
                    预算方管理
                  </Link>
                </Menu.Item>
                <Menu.Item key="5">
                  <Link to="/budgetManager/adSource">
                    广告源管理
                  </Link>
                </Menu.Item>
              </Menu.SubMenu>
              <Menu.SubMenu key="flow" icon={<UserOutlined />} title="媒体管理">
                <Menu.Item key="6">
                  <Link to="/flowManager/management">
                    流量管理
                  </Link>
                </Menu.Item>
                <Menu.Item key="7">
                  <Link to="/flowManager/media">
                    媒体管理
                  </Link>
                </Menu.Item>
                <Menu.Item key="8">
                  <Link to="/flowManager/adSlot">
                    广告位管理
                  </Link>
                </Menu.Item>
                <Menu.Item key="9">
                  <Link to="/flowManager/adCode">
                    代码位管理
                  </Link>
                </Menu.Item>
              </Menu.SubMenu>
              {/*<Menu.Item key="10" icon={<LogoutOutlined />}>*/}
              {/*  <Link to="/operationLog">*/}
              {/*    操作日志*/}
              {/*  </Link>*/}
              {/*</Menu.Item>*/}
            </Menu>
          </div>
        </Sider>
        <Layout style={{ flex: 1 }}>
          <Content style={{
            margin: 0,
            flex: 1,
            marginTop: 64
          }}>
            <div style={{ height: '100vh', overflowY: 'auto' }}>
              <AppRoutes />
            </div>
          </Content>
        </Layout>
      </Layout>
    </Layout>
  )
}